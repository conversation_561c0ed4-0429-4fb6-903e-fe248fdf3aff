//+------------------------------------------------------------------+
//|                    V75_1s_SpikeHunter_Fixed.mq5                 |
//|                   Fixed Trading EA for V75 1s Index             |
//|                   Trading on 1-minute timeframe                 |
//|                   Fixed Lot Size Issues                         |
//+------------------------------------------------------------------+
#property copyright "2025"
#property version   "5.2"
#property strict

// === V75 1s INDEX OPTIMIZED SWEET SPOT ZONES (1-MINUTE TIMEFRAME) ===
input group "=== OPTIMIZED SWEET SPOT ZONES ==="
input bool     UseSweetSpots = true;
// Optimized for actual V75 1s index behavior (50-1000+ point ranges)
input double   SweetSpot1_Min = 50.0;      // Small profitable spikes
input double   SweetSpot1_Max = 200.0;
input double   SweetSpot1_Ratio = 0.35;    // Lower body ratio requirement

input double   SweetSpot2_Min = 200.0;     // Medium spikes
input double   SweetSpot2_Max = 500.0;
input double   SweetSpot2_Ratio = 0.40;

input double   SweetSpot3_Min = 500.0;     // Large spikes
input double   SweetSpot3_Max = 1000.0;
input double   SweetSpot3_Ratio = 0.45;

input double   SweetSpot4_Min = 1000.0;    // Extra large spikes
input double   SweetSpot4_Max = 2000.0;
input double   SweetSpot4_Ratio = 0.50;

input group "=== BASE PARAMETERS ==="
input double   BaseSpikeSize = 50.0;       // Increased for V75 reality
input double   BaseBodyRatio = 0.35;       // Lowered to catch more signals
input double   BaseSpikeFactor = 1.2;      // Slightly reduced ATR factor

input group "=== TRADE MANAGEMENT ==="
input double   RiskPercent = 0.5;
input double   FixedLot = 0.1;             // Conservative lot size
input bool     UseFixedLot = true;          // Default to fixed lot to avoid volume errors
input double   MaxLot = 1.0;               // Maximum lot size limit
input double   RRRatio = 2.0;              // Balanced for better hit rate
input double   TrailPips = 8.0;            // Increased trailing distance
input int      MaxSlippage = 100;          // Increased for V75 volatility
input int      CooldownSeconds = 20;       // Moderate cooldown to reduce over-trading
input double   ATRMultiplier = 1.2;        // Slightly tighter SL
input double   SpikeMultiplier = 0.22;     // Slightly tighter SL

input group "=== SIGNAL FILTERING ==="
input bool     UseEMAFilter = true;        // Enable/disable EMA trend filter
input bool     UseContrarian = false;      // Trade against spikes instead of with them
input double   MinATRMultiplier = 1.0;     // Back to original working value
input bool     UseRSIFilter = true;        // RSI momentum confirmation
input double   RSI_Oversold = 40.0;        // RSI oversold level (relaxed for V75 1s)
input double   RSI_Overbought = 60.0;      // RSI overbought level (relaxed for V75 1s)
input bool     UseMACDFilter = true;       // MACD trend confirmation
input bool     UseStochFilter = true;      // Stochastic momentum filter
input bool     RequireMultiConfirm = false; // Require multiple confirmations (DISABLED for more trades)

input group "=== TRACKING ==="
input bool     TrackResults = true;
input bool     ShowDebug = true;
input bool     EnableTestMode = false;

// Global Variables
int atrHandle, emaFastHandle, emaSlowHandle;
int rsiHandle, macdHandle, stochHandle;
datetime lastTradeTime = 0;
int totalTrades = 0, winTrades = 0;
double lastATR = 0;
double minLot = 0, maxLot = 0, lotStep = 0;
double adjustedFixedLot = 0;

//+------------------------------------------------------------------+
int OnInit() {
   Print("=== V75 1s Index Spike Hunter v5.2 Fixed Initializing ===");
   
   // Get symbol trading specifications
   minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   
   Print("Symbol specs - Min Lot: ", minLot, " | Max Lot: ", maxLot, " | Lot Step: ", lotStep);
   
   // Validate and adjust FixedLot
   adjustedFixedLot = FixedLot; // Use a modifiable copy
   if(adjustedFixedLot < minLot) {
      Print("WARNING: FixedLot (", FixedLot, ") below minimum (", minLot, "). Adjusting to minimum.");
      adjustedFixedLot = minLot;
   }
   if(adjustedFixedLot > maxLot) {
      Print("WARNING: FixedLot (", FixedLot, ") above maximum (", maxLot, "). Adjusting to maximum.");
      adjustedFixedLot = maxLot;
   }
   
   // Ensure FixedLot aligns with lot step
   adjustedFixedLot = NormalizeLot(adjustedFixedLot);
   Print("Adjusted FixedLot: ", adjustedFixedLot);
   
   // Create indicators for 1-minute timeframe
   atrHandle = iATR(_Symbol, PERIOD_M1, 14);
   emaFastHandle = iMA(_Symbol, PERIOD_M1, 5, 0, MODE_EMA, PRICE_CLOSE);
   emaSlowHandle = iMA(_Symbol, PERIOD_M1, 13, 0, MODE_EMA, PRICE_CLOSE);

   // Initialize additional indicators for better signal quality
   if(UseRSIFilter) {
      rsiHandle = iRSI(_Symbol, PERIOD_M1, 14, PRICE_CLOSE);
      if(rsiHandle == INVALID_HANDLE) {
         Print("ERROR: Failed to create RSI indicator");
         return(INIT_FAILED);
      }
   }

   if(UseMACDFilter) {
      macdHandle = iMACD(_Symbol, PERIOD_M1, 12, 26, 9, PRICE_CLOSE);
      if(macdHandle == INVALID_HANDLE) {
         Print("ERROR: Failed to create MACD indicator");
         return(INIT_FAILED);
      }
   }

   if(UseStochFilter) {
      stochHandle = iStochastic(_Symbol, PERIOD_M1, 5, 3, 3, MODE_SMA, STO_LOWHIGH);
      if(stochHandle == INVALID_HANDLE) {
         Print("ERROR: Failed to create Stochastic indicator");
         return(INIT_FAILED);
      }
   }

   if(atrHandle == INVALID_HANDLE || emaFastHandle == INVALID_HANDLE || emaSlowHandle == INVALID_HANDLE) {
      Print("ERROR: Failed to create core indicators");
      return(INIT_FAILED);
   }
   
   // Wait for indicators to load
   Sleep(2000);
   
   PrintBrokerInfo();
   PrintOptimizedSettings();
   Print("=== EA Ready - V75 1s Multi-Confirmation Spike Hunter v5.3 ===");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
void OnTick() {
   static datetime lastBar = 0;
   datetime currentBar = iTime(_Symbol, PERIOD_M1, 0);
   if(lastBar == currentBar) return;
   lastBar = currentBar;
   
   // Safety checks
   if(PositionsTotal() > 0) return;
   if((TimeCurrent() - lastTradeTime) < CooldownSeconds) return;
   
   // Get fresh indicator data
   if(!GetIndicatorData()) return;
   
   // Analyze previous completed candle
   AnalyzeAndTrade();
   
   // Manage existing positions
   ManageTrailingStops();
}

//+------------------------------------------------------------------+
bool GetIndicatorData() {
   double atr[], emaFast[], emaSlow[];
   ArraySetAsSeries(atr, true);
   ArraySetAsSeries(emaFast, true);
   ArraySetAsSeries(emaSlow, true);

   if(CopyBuffer(atrHandle, 0, 0, 3, atr) < 3 ||
      CopyBuffer(emaFastHandle, 0, 0, 3, emaFast) < 3 ||
      CopyBuffer(emaSlowHandle, 0, 0, 3, emaSlow) < 3) {
      if(ShowDebug) Print("Failed to get indicator data");
      return false;
   }

   lastATR = atr[1];
   return true;
}

//+------------------------------------------------------------------+
void AnalyzeAndTrade() {
   // Get previous completed candle data (index 1)
   double open = iOpen(_Symbol, PERIOD_M1, 1);
   double high = iHigh(_Symbol, PERIOD_M1, 1);
   double low = iLow(_Symbol, PERIOD_M1, 1);
   double close = iClose(_Symbol, PERIOD_M1, 1);
   
   if(open == 0 || high == 0 || low == 0 || close == 0) return;
   
   // Calculate spike metrics
   double candleRange = (high - low) / _Point;
   double bodySize = MathAbs(close - open) / _Point;
   double bodyRatio = candleRange > 0 ? bodySize / candleRange : 0;
   bool isBullish = close > open;
   
   // Additional spike quality metrics
   double upperWick = (isBullish ? (high - close) : (high - open)) / _Point;
   double lowerWick = (isBullish ? (open - low) : (close - low)) / _Point;
   double atrPoints = lastATR / _Point;
   double atrMultiplier = candleRange / atrPoints;
   
   if(ShowDebug) {
      Print("V75 1s Analysis: Range=", DoubleToString(candleRange, 1), 
            " | Body%=", DoubleToString(bodyRatio*100, 1), 
            " | ATR×=", DoubleToString(atrMultiplier, 2),
            " | Bull=", isBullish);
   }
   
   // Check if valid spike
   int sweetSpotUsed = 0;
   if(!IsValidSpike(candleRange, bodyRatio, atrMultiplier, sweetSpotUsed)) return;

   // Multi-indicator confirmation check
   if(!GetMultiIndicatorConfirmation(isBullish)) {
      if(ShowDebug) Print("Trade rejected: Multi-indicator confirmation failed");
      return;
   }

   // Get trend direction and trade signal
   ENUM_ORDER_TYPE tradeType;
   if(!GetTradeSignal(isBullish, tradeType)) return;
   
   // Execute trade
   ExecuteTrade(tradeType, candleRange, bodyRatio, sweetSpotUsed, atrMultiplier);
}

//+------------------------------------------------------------------+
bool IsValidSpike(double candleRange, double bodyRatio, double atrMultiplier, int &sweetSpotUsed) {
   sweetSpotUsed = 0;
   
   // ATR filter - spike must be significant relative to recent volatility
   if(atrMultiplier < MinATRMultiplier) {
      if(ShowDebug) Print("Spike rejected: ATR multiplier too low (", DoubleToString(atrMultiplier, 2), ")");
      return false;
   }
   
   // Test mode for debugging
   if(EnableTestMode) {
      if(candleRange >= 30.0 && bodyRatio >= 0.25) {
         sweetSpotUsed = 99; // Test mode indicator
         if(ShowDebug) Print("TEST MODE: Valid spike detected - Range=", candleRange, " Ratio=", bodyRatio);
         return true;
      }
      return false;
   }
   
   if(UseSweetSpots) {
      // Check Sweet Spot 1 (Small profitable spikes: 50-200 points)
      if(candleRange >= SweetSpot1_Min && candleRange <= SweetSpot1_Max && bodyRatio >= SweetSpot1_Ratio) {
         sweetSpotUsed = 1;
         if(ShowDebug) Print("Sweet Spot 1 Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }
      
      // Check Sweet Spot 2 (Medium spikes: 200-500 points)
      if(candleRange >= SweetSpot2_Min && candleRange <= SweetSpot2_Max && bodyRatio >= SweetSpot2_Ratio) {
         sweetSpotUsed = 2;
         if(ShowDebug) Print("Sweet Spot 2 Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }
      
      // Check Sweet Spot 3 (Large spikes: 500-1000 points)
      if(candleRange >= SweetSpot3_Min && candleRange <= SweetSpot3_Max && bodyRatio >= SweetSpot3_Ratio) {
         sweetSpotUsed = 3;
         if(ShowDebug) Print("Sweet Spot 3 Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }
      
      // Check Sweet Spot 4 (Extra large spikes: 1000-2000 points)
      if(candleRange >= SweetSpot4_Min && candleRange <= SweetSpot4_Max && bodyRatio >= SweetSpot4_Ratio) {
         sweetSpotUsed = 4;
         if(ShowDebug) Print("Sweet Spot 4 Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }
      
      // Handle extreme spikes beyond defined ranges
      if(candleRange > SweetSpot4_Max && bodyRatio >= 0.30) {
         sweetSpotUsed = 5; // Extreme spike zone
         if(ShowDebug) Print("Extreme Spike Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }
   }
   else {
      // Base mode with updated parameters
      double bodySize = candleRange * bodyRatio;
      double atrPoints = lastATR / _Point;
      bool validSpike = candleRange >= BaseSpikeSize && 
                       bodyRatio >= BaseBodyRatio &&
                       bodySize >= (BaseSpikeFactor * atrPoints);
      
      if(validSpike && ShowDebug) {
         Print("Base Spike Hit: Range=", candleRange, " ATR=", DoubleToString(atrPoints,1), " Required=", BaseSpikeSize);
      }
      return validSpike;
   }
   
   return false;
}

//+------------------------------------------------------------------+
bool GetMultiIndicatorConfirmation(bool isBullish) {
   int confirmations = 0;
   int totalIndicators = 0;

   // RSI Confirmation
   if(UseRSIFilter) {
      totalIndicators++;
      double rsi[];
      ArraySetAsSeries(rsi, true);
      if(CopyBuffer(rsiHandle, 0, 0, 2, rsi) >= 2) {
         bool rsiConfirmed = false;
         string rsiSignal = "";

         if(isBullish && rsi[1] < RSI_Oversold) {
            confirmations++;
            rsiConfirmed = true;
            rsiSignal = "BUY_CONFIRMED";
         } else if(!isBullish && rsi[1] > RSI_Overbought) {
            confirmations++;
            rsiConfirmed = true;
            rsiSignal = "SELL_CONFIRMED";
         } else if(isBullish && rsi[1] > RSI_Overbought) {
            rsiSignal = "SELL_CONFLICT";
         } else if(!isBullish && rsi[1] < RSI_Oversold) {
            rsiSignal = "BUY_CONFLICT";
         } else {
            rsiSignal = "NEUTRAL";
         }

         if(ShowDebug) Print("RSI: ", DoubleToString(rsi[1], 1),
                           " | Zone: ", (rsi[1] < RSI_Oversold ? "OVERSOLD(<40)" : rsi[1] > RSI_Overbought ? "OVERBOUGHT(>60)" : "NEUTRAL"),
                           " | Signal: ", rsiSignal,
                           " | Confirmed: ", rsiConfirmed);
      }
   }

   // MACD Confirmation
   if(UseMACDFilter) {
      totalIndicators++;
      double macd[], signal[];
      ArraySetAsSeries(macd, true);
      ArraySetAsSeries(signal, true);
      if(CopyBuffer(macdHandle, 0, 0, 2, macd) >= 2 && CopyBuffer(macdHandle, 1, 0, 2, signal) >= 2) {
         bool macdBullish = macd[1] > signal[1] && macd[0] > macd[1]; // MACD above signal and rising
         bool macdBearish = macd[1] < signal[1] && macd[0] < macd[1]; // MACD below signal and falling
         bool macdConfirmed = false;
         string macdSignal = "";

         if(isBullish && macdBullish) {
            confirmations++;
            macdConfirmed = true;
            macdSignal = "BUY_CONFIRMED";
         } else if(!isBullish && macdBearish) {
            confirmations++;
            macdConfirmed = true;
            macdSignal = "SELL_CONFIRMED";
         } else if(isBullish && macdBearish) {
            macdSignal = "SELL_CONFLICT";
         } else if(!isBullish && macdBullish) {
            macdSignal = "BUY_CONFLICT";
         } else {
            macdSignal = "NEUTRAL";
         }

         if(ShowDebug) Print("MACD: ", DoubleToString(macd[1], 5),
                           " | Signal: ", DoubleToString(signal[1], 5),
                           " | Trend: ", (macdBullish ? "BULL" : macdBearish ? "BEAR" : "NEUTRAL"),
                           " | Result: ", macdSignal,
                           " | Confirmed: ", macdConfirmed);
      }
   }

   // Stochastic Confirmation
   if(UseStochFilter) {
      totalIndicators++;
      double stochMain[], stochSignal[];
      ArraySetAsSeries(stochMain, true);
      ArraySetAsSeries(stochSignal, true);
      if(CopyBuffer(stochHandle, 0, 0, 2, stochMain) >= 2 && CopyBuffer(stochHandle, 1, 0, 2, stochSignal) >= 2) {
         bool stochOversold = stochMain[1] < 30 && stochSignal[1] < 30;  // Relaxed for V75 1s
         bool stochOverbought = stochMain[1] > 70 && stochSignal[1] > 70; // Relaxed for V75 1s
         bool stochConfirmed = false;
         string stochResult = "";

         if(isBullish && stochOversold) {
            confirmations++;
            stochConfirmed = true;
            stochResult = "BUY_CONFIRMED";
         } else if(!isBullish && stochOverbought) {
            confirmations++;
            stochConfirmed = true;
            stochResult = "SELL_CONFIRMED";
         } else if(isBullish && stochOverbought) {
            stochResult = "SELL_CONFLICT";
         } else if(!isBullish && stochOversold) {
            stochResult = "BUY_CONFLICT";
         } else {
            stochResult = "NEUTRAL";
         }

         if(ShowDebug) Print("Stoch: ", DoubleToString(stochMain[1], 1),
                           " | Signal: ", DoubleToString(stochSignal[1], 1),
                           " | Zone: ", (stochOversold ? "OVERSOLD(<30)" : stochOverbought ? "OVERBOUGHT(>70)" : "NEUTRAL"),
                           " | Result: ", stochResult,
                           " | Confirmed: ", stochConfirmed);
      }
   }

   if(totalIndicators == 0) return true; // No additional filters enabled

   // Require at least 60% confirmation if RequireMultiConfirm is true
   double confirmationRatio = (double)confirmations / totalIndicators;
   bool confirmed = RequireMultiConfirm ? (confirmationRatio >= 0.6) : (confirmations > 0);

   if(ShowDebug) {
      Print("Multi-Indicator Analysis: ", confirmations, "/", totalIndicators,
            " (", DoubleToString(confirmationRatio * 100, 1), "%) | Required: ",
            (RequireMultiConfirm ? "60%" : "Any") , " | Result: ", (confirmed ? "PASS" : "FAIL"));
   }

   return confirmed;
}

//+------------------------------------------------------------------+
bool GetTradeSignal(bool isBullish, ENUM_ORDER_TYPE &tradeType) {
   // If EMA filter is disabled, trade based on spike direction only
   if(!UseEMAFilter) {
      if(UseContrarian) {
         // Trade against the spike (contrarian approach)
         tradeType = isBullish ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
         if(ShowDebug) Print("CONTRARIAN Signal: ", EnumToString(tradeType), " against ", (isBullish ? "bullish" : "bearish"), " spike");
      } else {
         // Trade with the spike (momentum approach)
         tradeType = isBullish ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
         if(ShowDebug) Print("MOMENTUM Signal: ", EnumToString(tradeType), " with ", (isBullish ? "bullish" : "bearish"), " spike");
      }
      return true;
   }
   
   // EMA trend filtering enabled
   double emaFast[], emaSlow[];
   ArraySetAsSeries(emaFast, true);
   ArraySetAsSeries(emaSlow, true);
   
   if(CopyBuffer(emaFastHandle, 0, 0, 2, emaFast) < 2 ||
      CopyBuffer(emaSlowHandle, 0, 0, 2, emaSlow) < 2) {
      return false;
   }
   
   bool upTrend = emaFast[1] > emaSlow[1];
   bool downTrend = emaFast[1] < emaSlow[1];
   
   if(UseContrarian) {
      // Contrarian approach with trend filter
      if(isBullish && downTrend) {
         tradeType = ORDER_TYPE_SELL;
         if(ShowDebug) Print("SELL Signal: Bullish spike reversal in downtrend");
         return true;
      }
      
      if(!isBullish && upTrend) {
         tradeType = ORDER_TYPE_BUY;
         if(ShowDebug) Print("BUY Signal: Bearish spike reversal in uptrend");
         return true;
      }
   } else {
      // Momentum approach with trend filter
      if(isBullish && upTrend) {
         tradeType = ORDER_TYPE_BUY;
         if(ShowDebug) Print("BUY Signal: Bullish spike in uptrend");
         return true;
      }
      
      if(!isBullish && downTrend) {
         tradeType = ORDER_TYPE_SELL;
         if(ShowDebug) Print("SELL Signal: Bearish spike in downtrend");
         return true;
      }
   }
   
   if(ShowDebug) Print("No valid signal: Spike/trend mismatch");
   return false;
}

//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE type, double spike, double bodyRatio, int sweetSpot, double atrMult) {
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double price = (type == ORDER_TYPE_BUY) ? ask : bid;

   // Calculate dynamic SL/TP based on spike size and ATR (slightly reduced from original)
   double slDistance = MathMax(lastATR * ATRMultiplier, spike * _Point * SpikeMultiplier);
   double sl = (type == ORDER_TYPE_BUY) ? price - slDistance : price + slDistance;
   double tp = (type == ORDER_TYPE_BUY) ? price + (slDistance * RRRatio) : price - (slDistance * RRRatio);
   
   double lot = CalculateLotSize(slDistance / _Point);
   
   ulong ticket = SendOrder(type, price, sl, tp, lot);
   if(ticket > 0) {
      lastTradeTime = TimeCurrent();
      totalTrades++;
      
      string spotName;
      switch(sweetSpot) {
         case 99: spotName = "TEST"; break;
         case 0:  spotName = "BASE"; break;
         case 5:  spotName = "EXTREME"; break;
         default: spotName = "SPOT" + (string)sweetSpot; break;
      }
      
      Print("TRADE EXECUTED: ", EnumToString(type), " | Zone: ", spotName, 
            " | Spike: ", DoubleToString(spike, 1), " (", DoubleToString(bodyRatio*100, 1), "%)",
            " | ATR×", DoubleToString(atrMult, 2), " | Lot: ", lot, " | Ticket: ", ticket);
   }
   else {
      Print("TRADE FAILED: Could not execute ", EnumToString(type), " order");
   }
}

//+------------------------------------------------------------------+
double NormalizeLot(double lot) {
   if(lotStep == 0) return lot;
   
   // Round to nearest lot step
   double normalized = MathRound(lot / lotStep) * lotStep;
   
   // Ensure within min/max bounds
   normalized = MathMax(minLot, MathMin(maxLot, normalized));
   
   return normalized;
}

//+------------------------------------------------------------------+
double CalculateLotSize(double riskPips) {
   if(UseFixedLot) {
      Print("Using fixed lot size: ", adjustedFixedLot);
      return adjustedFixedLot;
   }
   
   if(riskPips <= 0) {
      Print("Invalid risk pips, using fixed lot: ", adjustedFixedLot);
      return adjustedFixedLot;
   }
   
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   if(balance <= 0) {
      Print("Invalid balance, using fixed lot: ", adjustedFixedLot);
      return adjustedFixedLot;
   }
   
   double riskAmount = balance * RiskPercent / 100.0;
   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   
   if(tickSize <= 0 || tickValue <= 0) {
      Print("Invalid tick data, using fixed lot: ", adjustedFixedLot);
      return adjustedFixedLot;
   }
   
   double pointValue = tickValue * (_Point / tickSize);
   double calculatedLot = riskAmount / (riskPips * pointValue);
   
   // Apply maximum lot limit
   calculatedLot = MathMin(calculatedLot, MaxLot);
   
   // Normalize the lot size
   double finalLot = NormalizeLot(calculatedLot);
   
   if(ShowDebug) {
      Print("Lot calculation: Risk$=", DoubleToString(riskAmount, 2), 
            " | RiskPips=", DoubleToString(riskPips, 1),
            " | Calculated=", DoubleToString(calculatedLot, 3),
            " | Final=", DoubleToString(finalLot, 3));
   }
   
   return finalLot;
}

//+------------------------------------------------------------------+
ENUM_ORDER_TYPE_FILLING GetFillingMode() {
   int filling = (int)SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE);
   
   if((filling & SYMBOL_FILLING_FOK) != 0) return ORDER_FILLING_FOK;
   if((filling & SYMBOL_FILLING_IOC) != 0) return ORDER_FILLING_IOC;
   return ORDER_FILLING_FOK;
}

//+------------------------------------------------------------------+
ulong SendOrder(ENUM_ORDER_TYPE type, double price, double sl, double tp, double lot) {
   // Final validation of lot size before sending order
   if(lot < minLot || lot > maxLot) {
      Print("ERROR: Lot size ", lot, " outside valid range [", minLot, " - ", maxLot, "]. Using adjusted fixed lot.");
      lot = adjustedFixedLot;
   }
   
   // Ensure lot size is properly normalized
   lot = NormalizeLot(lot);
   
   MqlTradeRequest request = {};
   MqlTradeResult result = {};
   
   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = lot;
   request.type = type;
   request.price = NormalizeDouble(price, _Digits);
   request.sl = NormalizeDouble(sl, _Digits);
   request.tp = NormalizeDouble(tp, _Digits);
   request.deviation = MaxSlippage;
   request.magic = 654321;
   request.comment = "V75_1s_Spike_v5.2_Fixed";
   request.type_filling = GetFillingMode();
   
   Print("Sending order: ", EnumToString(type), " | Lot: ", lot, " | Price: ", price);
   
   bool success = OrderSend(request, result);
   
   if(!success) {
      Print("Order failed: ", result.comment, " | Retcode: ", result.retcode);
      
      // Try alternative filling mode
      if(result.retcode == TRADE_RETCODE_INVALID_FILL) {
         Print("Trying alternative filling mode...");
         request.type_filling = (request.type_filling == ORDER_FILLING_FOK) ? ORDER_FILLING_IOC : ORDER_FILLING_FOK;
         success = OrderSend(request, result);
         
         if(!success) {
            Print("Both filling modes failed. Trying market order without SL/TP...");
            request.sl = 0;
            request.tp = 0;
            success = OrderSend(request, result);
            
            if(success && (sl > 0 || tp > 0)) {
               Sleep(100);
               ModifyPosition(result.order, sl, tp);
            }
         }
      }
   }
   
   if(success) {
      Print("Order successful: Ticket ", result.order);
   } else {
      Print("Final order failure: ", result.comment, " | Retcode: ", result.retcode);
   }
   
   return success ? result.order : 0;
}

//+------------------------------------------------------------------+
void ManageTrailingStops() {
   for(int i = PositionsTotal()-1; i >= 0; i--) {
      ulong ticket = PositionGetTicket(i);
      if(!PositionSelectByTicket(ticket) || PositionGetString(POSITION_SYMBOL) != _Symbol) continue;
      
      double currentPrice = SymbolInfoDouble(_Symbol, (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? SYMBOL_BID : SYMBOL_ASK);
      double currentSL = PositionGetDouble(POSITION_SL);
      double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
      double trailDist = TrailPips * _Point;
      
      ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
      
      if(posType == POSITION_TYPE_BUY) {
         double newSL = currentPrice - trailDist;
         if(newSL > currentSL + _Point && newSL > openPrice) {
            ModifyPosition(ticket, newSL, PositionGetDouble(POSITION_TP));
            if(ShowDebug) Print("Trailing BUY SL updated to: ", newSL);
         }
      }
      else {
         double newSL = currentPrice + trailDist;
         if((currentSL == 0 || newSL < currentSL - _Point) && newSL < openPrice) {
            ModifyPosition(ticket, newSL, PositionGetDouble(POSITION_TP));
            if(ShowDebug) Print("Trailing SELL SL updated to: ", newSL);
         }
      }
   }
}

//+------------------------------------------------------------------+
bool ModifyPosition(ulong ticket, double sl, double tp) {
   MqlTradeRequest request = {};
   MqlTradeResult result = {};
   
   request.action = TRADE_ACTION_SLTP;
   request.position = ticket;
   request.sl = NormalizeDouble(sl, _Digits);
   request.tp = NormalizeDouble(tp, _Digits);
   
   return OrderSend(request, result);
}

//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction &trans, const MqlTradeRequest &request, const MqlTradeResult &result) {
   // Only process position closing transactions
   if(trans.type == TRADE_TRANSACTION_DEAL_ADD) {

      if(HistoryDealSelect(trans.deal)) {
         // Check if this is a closing deal (exit from position)
         ENUM_DEAL_ENTRY dealEntry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(trans.deal, DEAL_ENTRY);

         if(dealEntry == DEAL_ENTRY_OUT) {
            double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
            double swap = HistoryDealGetDouble(trans.deal, DEAL_SWAP);
            double commission = HistoryDealGetDouble(trans.deal, DEAL_COMMISSION);
            double totalProfit = profit + swap + commission;

            if(totalProfit > 0.01) winTrades++; // Consider small positive as win

            Print("TRADE CLOSED: ", (totalProfit > 0.01) ? "WIN" : "LOSS",
                  " | Profit: $", DoubleToString(totalProfit, 2),
                  " | P&L: $", DoubleToString(profit, 2),
                  " | Swap: $", DoubleToString(swap, 2),
                  " | Comm: $", DoubleToString(commission, 2));
            PrintStats();
         }
      }
   }
}

//+------------------------------------------------------------------+
void PrintStats() {
   double winRate = totalTrades > 0 ? (double)winTrades / totalTrades * 100.0 : 0;
   Print("=== V75 1s OPTIMIZED TRADING STATS ===");
   Print("Total Trades: ", totalTrades);
   Print("Wins: ", winTrades, " (", DoubleToString(winRate, 1), "%)");
   Print("Current ATR: ", DoubleToString(lastATR/_Point, 1), " points");
   Print("======================================");
}

//+------------------------------------------------------------------+
void PrintBrokerInfo() {
   Print("=== BROKER INFO ===");
   Print("Symbol: ", _Symbol);
   Print("Point: ", _Point, " | Digits: ", _Digits);
   Print("Min Lot: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN));
   Print("Max Lot: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX));
   Print("Lot Step: ", SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP));
   Print("===================");
}

//+------------------------------------------------------------------+
void PrintOptimizedSettings() {
   Print("=== V75 1s EA OPTIMIZED SETTINGS ===");
   Print("Sweet Spots: ", UseSweetSpots ? "ENABLED" : "DISABLED");
   Print("EMA Filter: ", UseEMAFilter ? "ENABLED" : "DISABLED");
   Print("Strategy: ", UseContrarian ? "CONTRARIAN" : "MOMENTUM");
   Print("Test Mode: ", EnableTestMode ? "ENABLED" : "DISABLED");
   Print("Use Fixed Lot: ", UseFixedLot ? "YES" : "NO");
   Print("Fixed Lot Size: ", adjustedFixedLot);
   Print("Max Lot Limit: ", MaxLot);
   
   if(UseSweetSpots && !EnableTestMode) {
      Print("Spot1: ", SweetSpot1_Min, "-", SweetSpot1_Max, " pts (", DoubleToString(SweetSpot1_Ratio*100,1), "% body)");
      Print("Spot2: ", SweetSpot2_Min, "-", SweetSpot2_Max, " pts (", DoubleToString(SweetSpot2_Ratio*100,1), "% body)");
      Print("Spot3: ", SweetSpot3_Min, "-", SweetSpot3_Max, " pts (", DoubleToString(SweetSpot3_Ratio*100,1), "% body)");
      Print("Spot4: ", SweetSpot4_Min, "-", SweetSpot4_Max, " pts (", DoubleToString(SweetSpot4_Ratio*100,1), "% body)");
   }
   
   Print("Risk: ", RiskPercent, "% | RR: ", RRRatio, " | Trail: ", TrailPips, " pips");
   Print("ATR×: ", ATRMultiplier, " | Spike×: ", SpikeMultiplier);
   Print("Cooldown: ", CooldownSeconds, "s | Min ATR×: ", MinATRMultiplier);
   Print("FILTERS: RSI=", UseRSIFilter, " MACD=", UseMACDFilter, " Stoch=", UseStochFilter);
   Print("Multi-Confirm Required: ", RequireMultiConfirm, " (60% threshold)");
   Print("====================================");
}

//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
   if(atrHandle != INVALID_HANDLE) IndicatorRelease(atrHandle);
   if(emaFastHandle != INVALID_HANDLE) IndicatorRelease(emaFastHandle);
   if(emaSlowHandle != INVALID_HANDLE) IndicatorRelease(emaSlowHandle);
   if(rsiHandle != INVALID_HANDLE) IndicatorRelease(rsiHandle);
   if(macdHandle != INVALID_HANDLE) IndicatorRelease(macdHandle);
   if(stochHandle != INVALID_HANDLE) IndicatorRelease(stochHandle);

   PrintStats();
   Print("=== V75 1s Spike Hunter v5.3 Multi-Confirm Stopped ===");
}