//+------------------------------------------------------------------+
//|                V75_1s_SpikeHunter_RELAXED.mq5                   |
//|          🚀 RELAXED VERSION - CATCH MORE SPIKES & PROFIT!       |
//|                   Trading on 1-minute timeframe                 |
//|          📉 Loosened constraints for better trade frequency     |
//+------------------------------------------------------------------+

#property copyright "V75 1s Spike Hunter"
#property version   "5.3"
#property strict

input group "=== RELAXED SWEET SPOT ZONES - CATCH MORE SPIKES ==="
input bool     UseSweetSpots = true;
// 📉 RELAXED for V75 1s - Catch smaller spikes and lower body ratios
input double   SweetSpot1_Min = 30.0;      // 📉 SMALLER spikes (was 50)
input double   SweetSpot1_Max = 200.0;
input double   SweetSpot1_Ratio = 0.25;    // 📉 MUCH LOWER body ratio (was 0.35)

input double   SweetSpot2_Min = 200.0;     // Medium spikes
input double   SweetSpot2_Max = 500.0;
input double   SweetSpot2_Ratio = 0.30;    // 📉 LOWER (was 0.40)

input double   SweetSpot3_Min = 500.0;     // Large spikes
input double   SweetSpot3_Max = 1000.0;
input double   SweetSpot3_Ratio = 0.35;    // 📉 LOWER (was 0.45)

input double   SweetSpot4_Min = 1000.0;    // Extra large spikes
input double   SweetSpot4_Max = 2000.0;
input double   SweetSpot4_Ratio = 0.40;    // 📉 LOWER (was 0.50)

input group "=== EMA TREND FILTER ==="
input int      EMA_Fast = 21;
input int      EMA_Slow = 50;

input group "=== TRADE MANAGEMENT ==="
input double   RiskPercent = 0.5;
input double   FixedLot = 0.1;             // Conservative lot size
input bool     UseFixedLot = true;          // Default to fixed lot to avoid volume errors
input double   MaxLot = 1.0;               // Maximum lot size limit
input double   RRRatio = 2.0;              // Balanced for better hit rate
input double   TrailPips = 8.0;            // Increased trailing distance
input int      MaxSlippage = 100;          // Increased for V75 volatility
input int      CooldownSeconds = 10;       // 📉 REDUCED cooldown - More opportunities (was 20)
input double   ATRMultiplier = 1.2;        // Slightly tighter SL
input double   SpikeMultiplier = 0.22;     // Slightly tighter SL

input group "=== SIGNAL FILTERING ==="
input bool     UseEMAFilter = false;       // 🚫 DISABLED - Too restrictive, blocking profitable trades
input bool     UseContrarian = false;      // Trade WITH spikes (momentum/trend-following)
input double   MinATRMultiplier = 0.8;     // 📉 RELAXED - Catch smaller spikes too
input bool     UseRSIFilter = true;        // RSI momentum confirmation
input double   RSI_Oversold = 35.0;        // 📉 MORE RELAXED - Catch more oversold conditions
input double   RSI_Overbought = 65.0;      // 📈 MORE RELAXED - Catch more overbought conditions
input bool     UseMACDFilter = true;       // MACD trend confirmation
input bool     UseStochFilter = true;      // Stochastic momentum filter
input bool     RequireMultiConfirm = true;  // Require multiple confirmations but RELAXED
input int      MinConfirmations = 1;         // 📉 RELAXED - Only need 1 confirmation instead of 2

input group "=== MACD SETTINGS ==="
input int      MACD_Fast = 12;
input int      MACD_Slow = 26;
input int      MACD_Signal = 9;

input group "=== STOCHASTIC SETTINGS ==="
input int      Stoch_K = 5;
input int      Stoch_D = 3;
input int      Stoch_Slowing = 3;

input group "=== RSI SETTINGS ==="
input int      RSI_Period = 14;

input group "=== ATR SETTINGS ==="
input int      ATR_Period = 14;

input group "=== TRACKING ==="
input bool     TrackResults = true;
input bool     ShowDebug = true;
input bool     EnableTestMode = false;

// Global Variables
int atrHandle, emaFastHandle, emaSlowHandle;
int rsiHandle, macdHandle, stochHandle;
datetime lastTradeTime = 0;
int totalTrades = 0, winTrades = 0;
double lastATR = 0;
double minLot = 0, maxLot = 0, lotStep = 0;
double adjustedFixedLot = 0;

// REWARD-BASED LEARNING SYSTEM
struct IndicatorCombo {
   string pattern;        // "RSI+MACD", "MACD+STOCH", "RSI+STOCH", "ALL_THREE"
   int trades;           // Total trades with this combo
   double totalProfit;   // Total profit/loss
   double winRate;       // Win percentage
   double avgProfit;     // Average profit per trade
   double score;         // Performance score
};

IndicatorCombo combos[4];
bool learningInitialized = false;
string lastTradePattern = "";  // Track what pattern was used for last trade

//+------------------------------------------------------------------+
//| Initialize Learning System                                       |
//+------------------------------------------------------------------+
void InitializeLearning()
{
   if(learningInitialized) return;
   
   // Initialize indicator combinations
   combos[0].pattern = "RSI+MACD";
   combos[1].pattern = "MACD+STOCH"; 
   combos[2].pattern = "RSI+STOCH";
   combos[3].pattern = "ALL_THREE";
   
   for(int i = 0; i < 4; i++) {
      combos[i].trades = 0;
      combos[i].totalProfit = 0.0;
      combos[i].winRate = 0.0;
      combos[i].avgProfit = 0.0;
      combos[i].score = 0.0;
   }
   
   learningInitialized = true;
   Print("🧠 REWARD-BASED LEARNING SYSTEM INITIALIZED");
}

//+------------------------------------------------------------------+
//| Update Learning System with Trade Result                        |
//+------------------------------------------------------------------+
void UpdateLearning(string pattern, double profit)
{
   if(!learningInitialized || pattern == "") return;
   
   // Find the combo index
   int comboIndex = -1;
   for(int i = 0; i < 4; i++) {
      if(combos[i].pattern == pattern || 
         (pattern == "RSI+MACD+STOCH" && combos[i].pattern == "ALL_THREE")) {
         comboIndex = i;
         break;
      }
   }
   
   if(comboIndex == -1) return; // Pattern not found
   
   // Update statistics
   combos[comboIndex].trades++;
   combos[comboIndex].totalProfit += profit;
   combos[comboIndex].avgProfit = combos[comboIndex].totalProfit / combos[comboIndex].trades;
   
   // Calculate win rate
   if(profit > 0) {
      combos[comboIndex].winRate = ((combos[comboIndex].winRate * (combos[comboIndex].trades - 1)) + 100.0) / combos[comboIndex].trades;
   } else {
      combos[comboIndex].winRate = (combos[comboIndex].winRate * (combos[comboIndex].trades - 1)) / combos[comboIndex].trades;
   }
   
   // Calculate performance score (win rate * avg profit)
   combos[comboIndex].score = combos[comboIndex].winRate * combos[comboIndex].avgProfit;
   
   Print("📊 LEARNING UPDATE: ", pattern, " | Profit: $", DoubleToString(profit, 2),
         " | Trades: ", combos[comboIndex].trades,
         " | Win Rate: ", DoubleToString(combos[comboIndex].winRate, 1), "%",
         " | Avg Profit: $", DoubleToString(combos[comboIndex].avgProfit, 2),
         " | Score: ", DoubleToString(combos[comboIndex].score, 2));
}

//+------------------------------------------------------------------+
//| Get Best Performing Pattern and Adjust Strategy                 |
//+------------------------------------------------------------------+
string GetBestPattern()
{
   if(!learningInitialized) return "";
   
   double bestScore = -999999;
   string bestPattern = "";
   
   for(int i = 0; i < 4; i++) {
      if(combos[i].trades >= 5 && combos[i].score > bestScore) { // Need at least 5 trades for reliability
         bestScore = combos[i].score;
         bestPattern = combos[i].pattern;
      }
   }
   
   return bestPattern;
}

//+------------------------------------------------------------------+
//| Print Learning Statistics                                        |
//+------------------------------------------------------------------+
void PrintLearningStats()
{
   if(!learningInitialized) return;
   
   Print("🧠 === LEARNING SYSTEM PERFORMANCE ===");
   for(int i = 0; i < 4; i++) {
      if(combos[i].trades > 0) {
         Print("📈 ", combos[i].pattern, ": ", combos[i].trades, " trades | ",
               DoubleToString(combos[i].winRate, 1), "% win | $",
               DoubleToString(combos[i].avgProfit, 2), " avg | Score: ",
               DoubleToString(combos[i].score, 2));
      }
   }
   
   string best = GetBestPattern();
   if(best != "") {
      Print("🏆 BEST PATTERN: ", best);
   }
}

//+------------------------------------------------------------------+
//| Check for Closed Trades and Update Learning                     |
//+------------------------------------------------------------------+
void CheckClosedTrades()
{
   static int lastDealsTotal = 0;
   int currentDealsTotal = HistoryDealsTotal();
   
   if(currentDealsTotal > lastDealsTotal) {
      // New deals found, check the latest ones
      for(int i = lastDealsTotal; i < currentDealsTotal; i++) {
         ulong ticket = HistoryDealGetTicket(i);
         if(ticket > 0) {
            string symbol = HistoryDealGetString(ticket, DEAL_SYMBOL);
            if(symbol == _Symbol) {
               long magic = HistoryDealGetInteger(ticket, DEAL_MAGIC);
               if(magic == 654321) { // Our EA's magic number
                  double profit = HistoryDealGetDouble(ticket, DEAL_PROFIT);
                  string comment = HistoryDealGetString(ticket, DEAL_COMMENT);
                  
                  // Extract pattern from comment
                  string pattern = "";
                  if(StringFind(comment, "Pattern:") >= 0) {
                     pattern = StringSubstr(comment, 8); // Remove "Pattern:" prefix
                  }
                  
                  if(pattern != "") {
                     UpdateLearning(pattern, profit);
                  }
               }
            }
         }
      }
   }
   
   lastDealsTotal = currentDealsTotal;
}

//+------------------------------------------------------------------+
int OnInit() {
   Print("🚀 === V75 1s RELAXED Spike Hunter v5.3 - CATCH MORE SPIKES! ===");
   
   // Initialize reward-based learning system
   InitializeLearning();
   
   // Get symbol trading specifications
   minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   
   // Validate and adjust fixed lot size
   if(UseFixedLot) {
      adjustedFixedLot = MathMax(minLot, MathMin(FixedLot, MaxLot));
      adjustedFixedLot = NormalizeLot(adjustedFixedLot);
      Print("Fixed lot adjusted from ", FixedLot, " to ", adjustedFixedLot);
   }
   
   // Initialize indicators
   atrHandle = iATR(_Symbol, PERIOD_M1, ATR_Period);
   if(UseEMAFilter) {
      emaFastHandle = iMA(_Symbol, PERIOD_M1, EMA_Fast, 0, MODE_EMA, PRICE_CLOSE);
      emaSlowHandle = iMA(_Symbol, PERIOD_M1, EMA_Slow, 0, MODE_EMA, PRICE_CLOSE);
   }
   
   if(UseRSIFilter) {
      rsiHandle = iRSI(_Symbol, PERIOD_M1, RSI_Period, PRICE_CLOSE);
   }
   
   if(UseMACDFilter) {
      macdHandle = iMACD(_Symbol, PERIOD_M1, MACD_Fast, MACD_Slow, MACD_Signal, PRICE_CLOSE);
   }
   
   if(UseStochFilter) {
      stochHandle = iStochastic(_Symbol, PERIOD_M1, Stoch_K, Stoch_D, Stoch_Slowing, MODE_SMA, STO_LOWHIGH);
   }
   
   // Validate handles
   if(atrHandle == INVALID_HANDLE) {
      Print("Failed to create ATR indicator");
      return(INIT_FAILED);
   }
   
   if(UseEMAFilter && (emaFastHandle == INVALID_HANDLE || emaSlowHandle == INVALID_HANDLE)) {
      Print("Failed to create EMA indicators");
      return(INIT_FAILED);
   }
   
   if(UseRSIFilter && rsiHandle == INVALID_HANDLE) {
      Print("Failed to create RSI indicator");
      return(INIT_FAILED);
   }
   
   if(UseMACDFilter && macdHandle == INVALID_HANDLE) {
      Print("Failed to create MACD indicator");
      return(INIT_FAILED);
   }
   
   if(UseStochFilter && stochHandle == INVALID_HANDLE) {
      Print("Failed to create Stochastic indicator");
      return(INIT_FAILED);
   }
   
   Sleep(2000);

   PrintBrokerInfo();
   PrintOptimizedSettings();
   Print("🎯 === EA READY - V75 1s RELAXED Spike Hunter - LET'S CATCH SPIKES! ===");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
void OnTick() {
   static datetime lastBar = 0;
   datetime currentBar = iTime(_Symbol, PERIOD_M1, 0);
   if(lastBar == currentBar) return;
   lastBar = currentBar;

   // Check for closed trades and update learning system
   CheckClosedTrades();

   // Safety checks
   if(PositionsTotal() > 0) return;
   if((TimeCurrent() - lastTradeTime) < CooldownSeconds) return;

   // Get fresh indicator data
   if(!GetIndicatorData()) return;

   // Analyze previous completed candle
   AnalyzeAndTrade();

   // Manage existing positions
   ManageTrailingStops();

   // Print learning stats every 100 ticks
   static int tickCount = 0;
   tickCount++;
   if(tickCount % 100 == 0) {
      PrintLearningStats();
   }
}

//+------------------------------------------------------------------+
bool GetIndicatorData() {
   double atr[];
   ArraySetAsSeries(atr, true);

   if(CopyBuffer(atrHandle, 0, 0, 3, atr) < 3) {
      if(ShowDebug) Print("Failed to get ATR data");
      return false;
   }

   lastATR = atr[1];
   return true;
}

//+------------------------------------------------------------------+
void AnalyzeAndTrade() {
   // Get previous completed candle data (index 1)
   double open = iOpen(_Symbol, PERIOD_M1, 1);
   double high = iHigh(_Symbol, PERIOD_M1, 1);
   double low = iLow(_Symbol, PERIOD_M1, 1);
   double close = iClose(_Symbol, PERIOD_M1, 1);

   if(open == 0 || high == 0 || low == 0 || close == 0) return;

   // Calculate spike metrics
   double candleRange = (high - low) / _Point;
   double bodySize = MathAbs(close - open) / _Point;
   double bodyRatio = candleRange > 0 ? bodySize / candleRange : 0;
   bool isBullish = close > open;

   // Additional spike quality metrics
   double atrPoints = lastATR / _Point;
   double atrMultiplier = candleRange / atrPoints;

   if(ShowDebug) {
      Print("V75 1s Analysis: Range=", DoubleToString(candleRange, 1),
            " | Body%=", DoubleToString(bodyRatio*100, 1),
            " | ATR×=", DoubleToString(atrMultiplier, 2),
            " | Bull=", isBullish);
   }

   // Check if valid spike
   int sweetSpotUsed = 0;
   if(!IsValidSpike(candleRange, bodyRatio, atrMultiplier, sweetSpotUsed)) {
      if(ShowDebug) Print("❌ SPIKE REJECTED: Range=", DoubleToString(candleRange,1), " Body%=", DoubleToString(bodyRatio*100,1), " ATR×=", DoubleToString(atrMultiplier,2));
      return;
   }

   // Multi-indicator confirmation check
   if(!GetMultiIndicatorConfirmation(isBullish)) {
      if(ShowDebug) Print("❌ TRADE REJECTED: Multi-indicator confirmation failed");
      return;
   }

   // Get trend direction and trade signal
   ENUM_ORDER_TYPE tradeType;
   if(!GetTradeSignal(isBullish, tradeType)) {
      if(ShowDebug) Print("❌ TRADE REJECTED: No valid trade signal");
      return;
   }

   // Execute trade
   ExecuteTrade(tradeType, candleRange, bodyRatio, sweetSpotUsed, atrMultiplier);
}

//+------------------------------------------------------------------+
bool IsValidSpike(double candleRange, double bodyRatio, double atrMultiplier, int &sweetSpotUsed) {
   sweetSpotUsed = 0;

   // ATR filter - spike must be significant relative to recent volatility
   if(atrMultiplier < MinATRMultiplier) {
      if(ShowDebug) Print("Spike rejected: ATR multiplier too low (", DoubleToString(atrMultiplier, 2), ")");
      return false;
   }

   // Test mode for debugging
   if(EnableTestMode) {
      if(candleRange >= 30.0 && bodyRatio >= 0.25) {
         sweetSpotUsed = 99; // Test mode indicator
         if(ShowDebug) Print("TEST MODE: Valid spike detected - Range=", candleRange, " Ratio=", bodyRatio);
         return true;
      }
      return false;
   }

   if(UseSweetSpots) {
      // Check Sweet Spot 1 (Small profitable spikes: 30-200 points) - RELAXED
      if(candleRange >= SweetSpot1_Min && candleRange <= SweetSpot1_Max && bodyRatio >= SweetSpot1_Ratio) {
         sweetSpotUsed = 1;
         if(ShowDebug) Print("✅ Sweet Spot 1 Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }

      // Check Sweet Spot 2 (Medium spikes: 200-500 points)
      if(candleRange >= SweetSpot2_Min && candleRange <= SweetSpot2_Max && bodyRatio >= SweetSpot2_Ratio) {
         sweetSpotUsed = 2;
         if(ShowDebug) Print("✅ Sweet Spot 2 Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }

      // Check Sweet Spot 3 (Large spikes: 500-1000 points)
      if(candleRange >= SweetSpot3_Min && candleRange <= SweetSpot3_Max && bodyRatio >= SweetSpot3_Ratio) {
         sweetSpotUsed = 3;
         if(ShowDebug) Print("✅ Sweet Spot 3 Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }

      // Check Sweet Spot 4 (Extra large spikes: 1000-2000 points)
      if(candleRange >= SweetSpot4_Min && candleRange <= SweetSpot4_Max && bodyRatio >= SweetSpot4_Ratio) {
         sweetSpotUsed = 4;
         if(ShowDebug) Print("✅ Sweet Spot 4 Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }

      // Handle extreme spikes beyond defined ranges
      if(candleRange > SweetSpot4_Max && bodyRatio >= 0.25) { // RELAXED from 0.30 to 0.25
         sweetSpotUsed = 5; // Extreme spike zone
         if(ShowDebug) Print("✅ Extreme Spike Hit: Range=", candleRange, " Ratio=", DoubleToString(bodyRatio*100,1), "%");
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
bool GetMultiIndicatorConfirmation(bool isBullish) {
   int confirmations = 0;
   int totalIndicators = 0;

   // Declare confirmation variables for pattern tracking
   bool rsiConfirmed = false, macdConfirmed = false, stochConfirmed = false;

   // RSI Confirmation
   if(UseRSIFilter) {
      totalIndicators++;
      double rsi[];
      ArraySetAsSeries(rsi, true);
      if(CopyBuffer(rsiHandle, 0, 0, 2, rsi) >= 2) {
         string rsiSignal = "";

         if(isBullish && rsi[1] < RSI_Oversold) {
            confirmations++;
            rsiConfirmed = true;
            rsiSignal = "BUY_OVERSOLD";
         }
         else if(!isBullish && rsi[1] > RSI_Overbought) {
            confirmations++;
            rsiConfirmed = true;
            rsiSignal = "SELL_OVERBOUGHT";
         }

         if(ShowDebug && rsiConfirmed) {
            Print("RSI Confirmation: ", rsiSignal, " (RSI=", DoubleToString(rsi[1], 1), ")");
         }
      }
   }

   // MACD Confirmation
   if(UseMACDFilter) {
      totalIndicators++;
      double macd[], signal[];
      ArraySetAsSeries(macd, true);
      ArraySetAsSeries(signal, true);
      if(CopyBuffer(macdHandle, 0, 0, 2, macd) >= 2 && CopyBuffer(macdHandle, 1, 0, 2, signal) >= 2) {
         bool macdBullish = macd[1] > signal[1] && macd[0] > macd[1]; // MACD above signal and rising
         bool macdBearish = macd[1] < signal[1] && macd[0] < macd[1]; // MACD below signal and falling
         string macdSignal = "";

         if(isBullish && macdBullish) {
            confirmations++;
            macdConfirmed = true;
            macdSignal = "BUY_BULLISH";
         }
         else if(!isBullish && macdBearish) {
            confirmations++;
            macdConfirmed = true;
            macdSignal = "SELL_BEARISH";
         }

         if(ShowDebug && macdConfirmed) {
            Print("MACD Confirmation: ", macdSignal, " (MACD=", DoubleToString(macd[1], 5), " Signal=", DoubleToString(signal[1], 5), ")");
         }
      }
   }

   // Stochastic Confirmation
   if(UseStochFilter) {
      totalIndicators++;
      double stochMain[], stochSignal[];
      ArraySetAsSeries(stochMain, true);
      ArraySetAsSeries(stochSignal, true);
      if(CopyBuffer(stochHandle, 0, 0, 2, stochMain) >= 2 && CopyBuffer(stochHandle, 1, 0, 2, stochSignal) >= 2) {
         bool stochOversold = stochMain[1] < 30 && stochSignal[1] < 30;  // Relaxed for V75 1s
         bool stochOverbought = stochMain[1] > 70 && stochSignal[1] > 70; // Relaxed for V75 1s
         string stochResult = "";

         if(isBullish && stochOversold) {
            confirmations++;
            stochConfirmed = true;
            stochResult = "BUY_OVERSOLD";
         }
         else if(!isBullish && stochOverbought) {
            confirmations++;
            stochConfirmed = true;
            stochResult = "SELL_OVERBOUGHT";
         }

         if(ShowDebug && stochConfirmed) {
            Print("Stoch Confirmation: ", stochResult, " (K=", DoubleToString(stochMain[1], 1), " D=", DoubleToString(stochSignal[1], 1), ")");
         }
      }
   }

   if(totalIndicators == 0) return true; // No additional filters enabled

   // Require minimum confirmations based on learning system
   bool confirmed = RequireMultiConfirm ? (confirmations >= MinConfirmations) : (confirmations > 0);

   // Track which indicators confirmed for learning
   string confirmedPattern = "";
   if(UseRSIFilter && rsiConfirmed) confirmedPattern += "RSI+";
   if(UseMACDFilter && macdConfirmed) confirmedPattern += "MACD+";
   if(UseStochFilter && stochConfirmed) confirmedPattern += "STOCH+";
   if(StringLen(confirmedPattern) > 0) confirmedPattern = StringSubstr(confirmedPattern, 0, StringLen(confirmedPattern)-1);

   // Store pattern for trade tracking
   if(confirmed) lastTradePattern = confirmedPattern;

   if(ShowDebug) {
      Print("Multi-Indicator Analysis: ", confirmations, "/", totalIndicators,
            " | Pattern: ", confirmedPattern,
            " | Required: ", (RequireMultiConfirm ? IntegerToString(MinConfirmations) + "+" : "Any"),
            " | Result: ", (confirmed ? "PASS ✅" : "FAIL ❌"));
   }

   return confirmed;
}

//+------------------------------------------------------------------+
bool GetTradeSignal(bool isBullish, ENUM_ORDER_TYPE &tradeType) {
   // Since EMA filter is DISABLED, trade based on spike direction only
   if(!UseEMAFilter) {
      if(UseContrarian) {
         // Trade against the spike (contrarian approach)
         tradeType = isBullish ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
         if(ShowDebug) Print("✅ CONTRARIAN Signal: ", EnumToString(tradeType), " against ", (isBullish ? "bullish" : "bearish"), " spike");
      } else {
         // Trade with the spike (momentum approach) - PREFERRED
         tradeType = isBullish ? ORDER_TYPE_BUY : ORDER_TYPE_SELL;
         if(ShowDebug) Print("✅ MOMENTUM Signal: ", EnumToString(tradeType), " with ", (isBullish ? "bullish" : "bearish"), " spike");
      }
      return true;
   }

   // EMA trend filtering enabled (but disabled by default in relaxed version)
   double emaFast[], emaSlow[];
   ArraySetAsSeries(emaFast, true);
   ArraySetAsSeries(emaSlow, true);

   if(CopyBuffer(emaFastHandle, 0, 0, 2, emaFast) < 2 ||
      CopyBuffer(emaSlowHandle, 0, 0, 2, emaSlow) < 2) {
      return false;
   }

   bool upTrend = emaFast[1] > emaSlow[1];
   bool downTrend = emaFast[1] < emaSlow[1];

   if(UseContrarian) {
      // Contrarian approach with trend filter
      if(isBullish && downTrend) {
         tradeType = ORDER_TYPE_SELL;
         if(ShowDebug) Print("SELL Signal: Bullish spike reversal in downtrend");
         return true;
      }

      if(!isBullish && upTrend) {
         tradeType = ORDER_TYPE_BUY;
         if(ShowDebug) Print("BUY Signal: Bearish spike reversal in uptrend");
         return true;
      }
   } else {
      // Momentum approach with trend filter
      if(isBullish && upTrend) {
         tradeType = ORDER_TYPE_BUY;
         if(ShowDebug) Print("BUY Signal: Bullish spike in uptrend");
         return true;
      }

      if(!isBullish && downTrend) {
         tradeType = ORDER_TYPE_SELL;
         if(ShowDebug) Print("SELL Signal: Bearish spike in downtrend");
         return true;
      }
   }

   if(ShowDebug) Print("No valid signal: Spike/trend mismatch");
   return false;
}

//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_ORDER_TYPE type, double spike, double bodyRatio, int sweetSpot, double atrMult) {
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double price = (type == ORDER_TYPE_BUY) ? ask : bid;

   // Calculate dynamic SL/TP based on spike size and ATR
   double slDistance = MathMax(lastATR * ATRMultiplier, spike * _Point * SpikeMultiplier);
   double sl = (type == ORDER_TYPE_BUY) ? price - slDistance : price + slDistance;
   double tp = (type == ORDER_TYPE_BUY) ? price + (slDistance * RRRatio) : price - (slDistance * RRRatio);

   double lot = CalculateLotSize(slDistance / _Point);

   ulong ticket = SendOrder(type, price, sl, tp, lot);
   if(ticket > 0) {
      lastTradeTime = TimeCurrent();
      totalTrades++;

      string spotName;
      switch(sweetSpot) {
         case 99: spotName = "TEST"; break;
         case 0:  spotName = "BASE"; break;
         case 5:  spotName = "EXTREME"; break;
         default: spotName = "SPOT" + IntegerToString(sweetSpot); break;
      }

      Print("🚀 TRADE EXECUTED: ", EnumToString(type), " | Ticket: ", ticket,
            " | Lot: ", DoubleToString(lot, 2),
            " | Price: ", DoubleToString(price, _Digits),
            " | SL: ", DoubleToString(sl, _Digits),
            " | TP: ", DoubleToString(tp, _Digits),
            " | Zone: ", spotName,
            " | Pattern: ", lastTradePattern);
   }
}

//+------------------------------------------------------------------+
double NormalizeLot(double lot) {
   if(lotStep == 0) return lot;

   // Round to nearest lot step
   double normalized = MathRound(lot / lotStep) * lotStep;

   // Ensure within min/max bounds
   normalized = MathMax(minLot, MathMin(maxLot, normalized));

   return normalized;
}

//+------------------------------------------------------------------+
double CalculateLotSize(double riskPips) {
   if(UseFixedLot) {
      if(ShowDebug) Print("Using fixed lot size: ", adjustedFixedLot);
      return adjustedFixedLot;
   }

   if(riskPips <= 0) {
      Print("Invalid risk pips, using fixed lot: ", adjustedFixedLot);
      return adjustedFixedLot;
   }

   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   if(balance <= 0) {
      Print("Invalid balance, using fixed lot: ", adjustedFixedLot);
      return adjustedFixedLot;
   }

   double riskAmount = balance * RiskPercent / 100.0;
   double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

   if(tickSize <= 0 || tickValue <= 0) {
      Print("Invalid tick data, using fixed lot: ", adjustedFixedLot);
      return adjustedFixedLot;
   }

   double pointValue = tickValue * (_Point / tickSize);
   double calculatedLot = riskAmount / (riskPips * pointValue);

   // Apply maximum lot limit
   calculatedLot = MathMin(calculatedLot, MaxLot);

   // Normalize the lot size
   double finalLot = NormalizeLot(calculatedLot);

   if(ShowDebug) {
      Print("Lot calculation: Risk$=", DoubleToString(riskAmount, 2),
            " | RiskPips=", DoubleToString(riskPips, 1),
            " | Calculated=", DoubleToString(calculatedLot, 3),
            " | Final=", DoubleToString(finalLot, 3));
   }

   return finalLot;
}

//+------------------------------------------------------------------+
ENUM_ORDER_TYPE_FILLING GetFillingMode() {
   int filling = (int)SymbolInfoInteger(_Symbol, SYMBOL_FILLING_MODE);

   if((filling & SYMBOL_FILLING_FOK) != 0) return ORDER_FILLING_FOK;
   if((filling & SYMBOL_FILLING_IOC) != 0) return ORDER_FILLING_IOC;
   return ORDER_FILLING_FOK;
}

//+------------------------------------------------------------------+
ulong SendOrder(ENUM_ORDER_TYPE type, double price, double sl, double tp, double lot) {
   // Final validation of lot size before sending order
   if(lot < minLot || lot > maxLot) {
      Print("ERROR: Lot size ", lot, " outside valid range [", minLot, " - ", maxLot, "]. Using adjusted fixed lot.");
      lot = adjustedFixedLot;
   }

   // Ensure lot size is properly normalized
   lot = NormalizeLot(lot);

   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.symbol = _Symbol;
   request.volume = lot;
   request.type = type;
   request.price = NormalizeDouble(price, _Digits);
   request.sl = NormalizeDouble(sl, _Digits);
   request.tp = NormalizeDouble(tp, _Digits);
   request.deviation = MaxSlippage;
   request.magic = 654321;
   request.comment = "V75_RELAXED_v5.3";
   request.type_filling = GetFillingMode();

   Print("Sending order: ", EnumToString(type), " | Lot: ", lot, " | Price: ", price);

   bool success = OrderSend(request, result);

   if(!success) {
      Print("Order failed: ", result.comment, " | Retcode: ", result.retcode);

      // Try alternative filling mode
      if(result.retcode == TRADE_RETCODE_INVALID_FILL) {
         Print("Trying alternative filling mode...");
         request.type_filling = (request.type_filling == ORDER_FILLING_FOK) ? ORDER_FILLING_IOC : ORDER_FILLING_FOK;
         success = OrderSend(request, result);

         if(!success) {
            Print("Both filling modes failed. Trying market order without SL/TP...");
            request.sl = 0;
            request.tp = 0;
            success = OrderSend(request, result);

            if(success && (sl > 0 || tp > 0)) {
               Sleep(100);
               ModifyPosition(result.order, sl, tp);
            }
         }
      }
   }

   if(success) {
      Print("Order successful: Ticket ", result.order, " | Pattern: ", lastTradePattern);
      // Store the pattern with the ticket for later tracking
      request.comment = "Pattern:" + lastTradePattern;
   } else {
      Print("Final order failure: ", result.comment, " | Retcode: ", result.retcode);
   }

   return success ? result.order : 0;
}

//+------------------------------------------------------------------+
void ManageTrailingStops() {
   for(int i = PositionsTotal()-1; i >= 0; i--) {
      ulong ticket = PositionGetTicket(i);
      if(!PositionSelectByTicket(ticket) || PositionGetString(POSITION_SYMBOL) != _Symbol) continue;

      double currentPrice = SymbolInfoDouble(_Symbol, (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? SYMBOL_BID : SYMBOL_ASK);
      double currentSL = PositionGetDouble(POSITION_SL);
      double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
      double trailDist = TrailPips * _Point;

      ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

      if(posType == POSITION_TYPE_BUY) {
         double newSL = currentPrice - trailDist;
         if(newSL > currentSL + _Point && newSL > openPrice) {
            ModifyPosition(ticket, newSL, PositionGetDouble(POSITION_TP));
            if(ShowDebug) Print("Trailing BUY SL updated to: ", newSL);
         }
      }
      else {
         double newSL = currentPrice + trailDist;
         if((currentSL == 0 || newSL < currentSL - _Point) && newSL < openPrice) {
            ModifyPosition(ticket, newSL, PositionGetDouble(POSITION_TP));
            if(ShowDebug) Print("Trailing SELL SL updated to: ", newSL);
         }
      }
   }
}

//+------------------------------------------------------------------+
bool ModifyPosition(ulong ticket, double sl, double tp) {
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_SLTP;
   request.position = ticket;
   request.sl = NormalizeDouble(sl, _Digits);
   request.tp = NormalizeDouble(tp, _Digits);

   return OrderSend(request, result);
}

//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction &trans, const MqlTradeRequest &request, const MqlTradeResult &result) {
   // Only process position closing transactions
   if(trans.type == TRADE_TRANSACTION_DEAL_ADD) {

      if(HistoryDealSelect(trans.deal)) {
         // Check if this is a closing deal (exit from position)
         ENUM_DEAL_ENTRY dealEntry = (ENUM_DEAL_ENTRY)HistoryDealGetInteger(trans.deal, DEAL_ENTRY);

         if(dealEntry == DEAL_ENTRY_OUT) {
            double profit = HistoryDealGetDouble(trans.deal, DEAL_PROFIT);
            double swap = HistoryDealGetDouble(trans.deal, DEAL_SWAP);
            double commission = HistoryDealGetDouble(trans.deal, DEAL_COMMISSION);
            double totalProfit = profit + swap + commission;

            if(totalProfit > 0.01) winTrades++; // Consider small positive as win

            Print("TRADE CLOSED: ", (totalProfit > 0.01) ? "WIN ✅" : "LOSS ❌",
                  " | Profit: $", DoubleToString(totalProfit, 2),
                  " | P&L: $", DoubleToString(profit, 2),
                  " | Swap: $", DoubleToString(swap, 2),
                  " | Comm: $", DoubleToString(commission, 2));
            PrintStats();
         }
      }
   }
}

//+------------------------------------------------------------------+
void PrintStats() {
   double winRate = totalTrades > 0 ? (double)winTrades / totalTrades * 100.0 : 0;
   Print("🎯 === V75 1s RELAXED TRADING STATS ===");
   Print("Total Trades: ", totalTrades);
   Print("Winning Trades: ", winTrades);
   Print("Win Rate: ", DoubleToString(winRate, 1), "%");
   Print("========================================");
}

//+------------------------------------------------------------------+
void PrintBrokerInfo() {
   Print("=== BROKER INFORMATION ===");
   Print("Broker: ", AccountInfoString(ACCOUNT_COMPANY));
   Print("Account: ", AccountInfoInteger(ACCOUNT_LOGIN));
   Print("Balance: $", DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2));
   Print("Leverage: 1:", AccountInfoInteger(ACCOUNT_LEVERAGE));
   Print("Min Lot: ", minLot, " | Max Lot: ", maxLot, " | Step: ", lotStep);
}

//+------------------------------------------------------------------+
void PrintOptimizedSettings() {
   Print("🚀 === RELAXED SETTINGS - CATCH MORE SPIKES! ===");
   Print("EMA Filter: ", UseEMAFilter ? "ENABLED" : "🚫 DISABLED (More trades!)");
   Print("Contrarian: ", UseContrarian ? "YES" : "NO (Momentum trading)");
   Print("Min Confirmations: ", MinConfirmations, " (RELAXED from 2)");
   Print("Min ATR Multiplier: ", MinATRMultiplier, " (RELAXED)");
   Print("Cooldown: ", CooldownSeconds, "s (REDUCED)");
   Print("RSI Levels: ", RSI_Oversold, "/", RSI_Overbought, " (RELAXED)");

   if(UseSweetSpots) {
      Print("📉 RELAXED Sweet Spots:");
      Print("Spot1: ", SweetSpot1_Min, "-", SweetSpot1_Max, " pts (", DoubleToString(SweetSpot1_Ratio*100,1), "% body)");
      Print("Spot2: ", SweetSpot2_Min, "-", SweetSpot2_Max, " pts (", DoubleToString(SweetSpot2_Ratio*100,1), "% body)");
      Print("Spot3: ", SweetSpot3_Min, "-", SweetSpot3_Max, " pts (", DoubleToString(SweetSpot3_Ratio*100,1), "% body)");
      Print("Spot4: ", SweetSpot4_Min, "-", SweetSpot4_Max, " pts (", DoubleToString(SweetSpot4_Ratio*100,1), "% body)");
   }

   Print("Risk: ", RiskPercent, "% | RR: ", RRRatio, " | Trail: ", TrailPips, " pips");
   Print("ATR×: ", ATRMultiplier, " | Spike×: ", SpikeMultiplier);
   Print("🎯 READY TO CATCH SPIKES AND MAKE PROFIT!");
}
